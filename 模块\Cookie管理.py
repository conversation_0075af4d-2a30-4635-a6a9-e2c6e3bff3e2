import subprocess
import sys
import os
import json
import time
import random
from typing import Optional
from .配置管理 import 配置管理器

try:
    from playwright.sync_api import sync_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("警告: playwright未安装，无法使用自动Cookie获取功能")
    print("请运行: pip install playwright && playwright install chromium")

class Cookie管理器:
    def __init__(self):
        self.配置 = None
        self.cookie = ""  # 默认为空字符串而不是None
        self.配置目录 = os.path.dirname(os.path.abspath(__file__))
        self.cookie配置文件 = os.path.join(self.配置目录, 'cookie_config.json')
        
    def 设置配置目录(self, 配置目录: str) -> None:
        """设置配置文件保存目录"""
        self.配置目录 = 配置目录
        self.cookie配置文件 = os.path.join(self.配置目录, 'cookie_config.json')

        # 初始化配置管理器
        self.配置 = 配置管理器()
        self.配置.设置配置目录(self.配置目录)
        self.cookie = self.配置.获取Cookie()
        
    def 获取Cookie(self) -> str:
        """从配置文件获取Cookie"""
        try:
            # 如果配置未初始化，先尝试从配置管理器获取
            if self.配置 is not None:
                return self.配置.获取Cookie()

            # 否则从cookie配置文件获取
            if os.path.exists(self.cookie配置文件):
                with open(self.cookie配置文件, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('cookie', '')
            return ''
        except Exception as e:
            print(f"加载cookie配置文件失败: {e}")
            return ''
            
    def 保存Cookie(self, cookie: str) -> bool:
        """保存Cookie到配置文件"""
        try:
            with open(self.cookie配置文件, 'w', encoding='utf-8') as f:
                json.dump({'cookie': cookie}, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存cookie配置文件失败: {e}")
            return False
            
    def 刷新Cookie(self, 需要登录: bool = False) -> Optional[str]:
        """使用浏览器自动获取新Cookie

        Args:
            需要登录: 是否需要登录获取完整Cookie，默认False（仅访问页面获取基础Cookie）
        """
        if not PLAYWRIGHT_AVAILABLE:
            print("错误: playwright未安装，无法使用自动Cookie获取功能")
            print("请运行: pip install playwright && playwright install chromium")
            return None

        print("正在启动浏览器获取Cookie...")
        if 需要登录:
            print("模式: 需要登录获取完整Cookie")
        else:
            print("模式: 仅访问页面获取基础Cookie")

        try:
            with sync_playwright() as p:
                # 启动浏览器
                print("正在启动Chrome浏览器...")
                browser = p.chromium.launch(
                    headless=False,  # 显示浏览器窗口
                    args=[
                        '--disable-blink-features=AutomationControlled',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor'
                    ]
                )

                context = browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    viewport={'width': 1280, 'height': 720}
                )
                page = context.new_page()

                print("正在访问闲鱼网站获取Cookie...")
                # 访问闲鱼网站，等待页面加载完成
                page.goto("https://2.taobao.com/", wait_until="networkidle", timeout=30000)

                # 等待一下确保Cookie设置完成
                page.wait_for_timeout(2000)

                if 需要登录:
                    print("浏览器已打开，请在浏览器中登录闲鱼账号以获取完整Cookie")
                    print("登录完成后，请在控制台输入 'y' 并按回车键继续...")
                    print("如果要取消操作，请输入 'n'")

                    # 等待用户确认登录完成
                    while True:
                        try:
                            user_input = input("登录完成了吗？(y/n): ").strip().lower()
                            if user_input == 'y':
                                break
                            elif user_input == 'n':
                                print("用户取消了Cookie获取操作")
                                browser.close()
                                return None
                            else:
                                print("请输入 'y' 或 'n'")
                        except KeyboardInterrupt:
                            print("\n用户中断了操作")
                            browser.close()
                            return None
                else:
                    print("页面加载完成，正在获取基础Cookie...")

                # 获取所有Cookie
                cookies = context.cookies()

                # 构建Cookie字符串
                cookie_str = ""
                important_cookies = ['_m_h5_tk', 'cookie2', '_tb_token_', 'sgcookie', 'unb', 'uc1', 'uc3', 'uc4', 'mtop_partitioned_detect']

                for cookie in cookies:
                    # 获取所有闲鱼相关域名的Cookie
                    if (cookie['domain'] in ['.taobao.com', '2.taobao.com', '.goofish.com', 'h5api.m.goofish.com'] or
                        cookie['name'] in important_cookies):
                        cookie_str += f"{cookie['name']}={cookie['value']}; "

                # 移除末尾的分号和空格
                cookie_str = cookie_str.rstrip('; ')

                # 关闭浏览器
                browser.close()
                print("浏览器已关闭")

                if cookie_str:
                    # 检查是否包含基本的Cookie字段
                    has_basic_cookies = any(field in cookie_str for field in ['cookie2=', 'mtop_partitioned_detect='])

                    if has_basic_cookies:
                        # 保存新的Cookie
                        if self.保存Cookie(cookie_str):
                            print("Cookie获取并保存成功！")
                            print(f"Cookie长度: {len(cookie_str)} 字符")

                            # 显示获取到的主要Cookie字段
                            if '_m_h5_tk=' in cookie_str:
                                print("✓ 包含_m_h5_tk字段（API调用令牌）")
                            if 'cookie2=' in cookie_str:
                                print("✓ 包含cookie2字段（基础认证）")
                            if 'mtop_partitioned_detect=' in cookie_str:
                                print("✓ 包含mtop_partitioned_detect字段")

                            return cookie_str
                        else:
                            print("Cookie保存失败")
                            return None
                    else:
                        print("获取到的Cookie缺少必要字段，可能需要刷新页面或稍后重试")
                        return None
                else:
                    print("未能获取到任何Cookie")
                    return None

        except Exception as e:
            print(f"获取Cookie时出错: {e}")
            try:
                browser.close()
            except:
                pass
            return None